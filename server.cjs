const express = require('express');
const cors = require('cors');
const fs = require('fs').promises;
const path = require('path');
const QRCode = require('qrcode');
const { createCanvas } = require('canvas');
const crypto = require('crypto');
const os = require('os');
const PDFDocument = require('pdfkit');
const bidi = require('bidi-js');
const admin = require('firebase-admin');
const multer = require('multer');

// __dirname is available in CommonJS

// Initialize Firebase Admin
const firebaseConfig = {
  projectId: "textbook-platform",
  storageBucket: process.env.FIREBASE_STORAGE_BUCKET,
};

let firebaseInitialized = false;

try {
  admin.initializeApp({
    projectId: firebaseConfig.projectId,
    storageBucket: firebaseConfig.storageBucket,
  });
  firebaseInitialized = true;
  console.log('✅ Firebase Admin initialized');
} catch (error) {
  console.log('⚠️  Firebase Admin not initialized (credentials not available)');
  console.log('   Using fallback authentication for local development');
  firebaseInitialized = false;
}

const app = express();
const PORT = process.env.PORT || 8080;
const BOOKS_FILE = path.join(__dirname, 'public', 'books.json');

app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Configure multer for file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    // Check if file is an image
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Only image files are allowed'), false);
    }
  }
});

// Health check endpoint
app.get('/', (req, res) => {
  res.json({
    status: 'OK',
    message: 'Textbook Platform Server is running',
    timestamp: new Date().toISOString()
  });
});

app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    uptime: process.uptime(),
    timestamp: new Date().toISOString()
  });
});

// Helper function to read books from Firestore
async function readBooks() {
  try {
    if (!firebaseInitialized) {
      console.log('Firebase not initialized, using fallback empty array');
      return [];
    }

    const db = admin.firestore();
    const booksSnapshot = await db.collection('books').get();
    const books = [];

    for (const bookDoc of booksSnapshot.docs) {
      const bookData = bookDoc.data();
      const book = {
        id: bookDoc.id,
        title: bookData.title,
        description: bookData.description,
        theme: bookData.theme || { primaryColor: '#4f46e5', secondaryColor: '#7c3aed', accentColor: '#F59E0B' },
        chapters: []
      };
      console.log('Book data retrieved from Firestore:', book);

      // Get chapters for this book
      const chaptersSnapshot = await db.collection('books').doc(bookDoc.id).collection('chapters').get();
      for (const chapterDoc of chaptersSnapshot.docs) {
        const chapterData = chapterDoc.data();
        const chapter = {
          id: chapterDoc.id,
          title: chapterData.title,
          items: []
        };

        // Get items for this chapter
        const itemsSnapshot = await db.collection('books').doc(bookDoc.id).collection('chapters').doc(chapterDoc.id).collection('items').get();
        for (const itemDoc of itemsSnapshot.docs) {
          const itemData = itemDoc.data();
          // Ensure imageUrl is correctly set based on imagePath or url
          const finalImageUrl = itemData.imagePath ? `https://storage.googleapis.com/${admin.storage().bucket().name}/${itemData.imagePath}` : itemData.url || '';
          chapter.items.push({
            id: itemDoc.id,
            ...itemData,
            imageUrl: finalImageUrl,
          });
        }

        book.chapters.push(chapter);
      }

      books.push(book);
    }

    return books;
  } catch (error) {
    console.error('Error reading books from Firestore:', error);
    return [];
  }
}

// writeBooks function removed - all operations now use Firestore directly

// Helper function to generate meaningful IDs
function generateMeaningfulId(type, books, bookId = null, chapterId = null) {
  switch (type) {
    case 'book':
      const bookCount = books.length + 1;
      return `book${bookCount}`;

    case 'chapter':
      const book = books.find(b => b.id === bookId);
      if (!book) return `ch${Date.now()}`;
      const chapterCount = book.chapters.length + 1;
      return `${bookId}-ch${chapterCount}`;

    case 'item':
      const targetBook = books.find(b => b.id === bookId);
      if (!targetBook) return `item${Date.now()}`;
      const targetChapter = targetBook.chapters.find(c => c.id === chapterId);
      if (!targetChapter) return `item${Date.now()}`;
      const itemCount = targetChapter.items.length + 1;
      return `${chapterId}-item${itemCount}`;

    default:
      return Date.now().toString();
  }
}

// Generate secure token for QR access
function generateSecureToken() {
  return crypto.randomBytes(32).toString('hex');
}

// Utility function to shuffle an array using Fisher-Yates algorithm
function shuffleArray(array) {
  const shuffled = [...array];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
}

// Function to shuffle question options while maintaining correct answer tracking
function shuffleQuestionOptions(question) {
  if (!question.options || question.options.length <= 1) {
    return question;
  }

  // Create array of options with their original indices
  const optionsWithIndices = question.options.map((option, index) => ({
    option,
    originalIndex: index
  }));

  // Shuffle the options
  const shuffledOptionsWithIndices = shuffleArray(optionsWithIndices);

  // Extract shuffled options and find new correct answer index
  const shuffledOptions = shuffledOptionsWithIndices.map(item => item.option);
  const newCorrectAnswerIndex = shuffledOptionsWithIndices.findIndex(
    item => item.originalIndex === question.correctAnswer
  );

  return {
    ...question,
    options: shuffledOptions,
    correctAnswer: newCorrectAnswerIndex,
    originalCorrectAnswer: question.correctAnswer // Keep track of original for reference
  };
}

// Get the network IP address
function getNetworkIP() {
  const interfaces = os.networkInterfaces();
  for (const name of Object.keys(interfaces)) {
    for (const iface of interfaces[name]) {
      // Skip over non-IPv4 and internal (i.e. 127.0.0.1) addresses
      if (iface.family === 'IPv4' && !iface.internal) {
        return iface.address;
      }
    }
  }
  return 'localhost'; // fallback
}

// Store for QR tokens (in production, use Redis or database)
const qrTokens = new Map();

// Store for user sessions (in production, use Redis or database)
const userSessions = new Map();

// QR Code Cache Management Functions
class QRCodeCache {
  /**
   * Generate a cache key for a QR code based on the content
   * @param {string} data - The QR code data/content
   * @returns {string} - The cache key
   */
  static generateCacheKey(data) {
    // Create a hash of the QR code data for consistent caching
    return crypto.createHash('sha256').update(data).digest('hex');
  }

  /**
   * Generate the storage path for a cached QR code
   * @param {string} cacheKey - The cache key
   * @returns {string} - The storage path
   */
  static getCachePath(cacheKey) {
    return `qr-cache/${cacheKey}.svg`;
  }

  /**
   * Check if a QR code is cached in Google Cloud Storage
   * @param {string} cacheKey - The cache key
   * @returns {Promise<boolean>} - Whether the QR code is cached
   */
  static async isCached(cacheKey) {
    if (!firebaseInitialized) {
      return false;
    }

    try {
      const bucket = admin.storage().bucket();
      const file = bucket.file(this.getCachePath(cacheKey));
      const [exists] = await file.exists();
      return exists;
    } catch (error) {
      console.error('Error checking QR cache:', error);
      return false;
    }
  }

  /**
   * Get cached QR code SVG content
   * @param {string} cacheKey - The cache key
   * @returns {Promise<string|null>} - The SVG content or null if not found
   */
  static async getCachedQR(cacheKey) {
    if (!firebaseInitialized) {
      return null;
    }

    try {
      const bucket = admin.storage().bucket();
      const file = bucket.file(this.getCachePath(cacheKey));

      const [exists] = await file.exists();
      if (!exists) {
        return null;
      }

      const [content] = await file.download();
      return content.toString('utf8');
    } catch (error) {
      console.error('Error retrieving cached QR code:', error);
      return null;
    }
  }

  /**
   * Cache a QR code SVG in Google Cloud Storage
   * @param {string} cacheKey - The cache key
   * @param {string} svgContent - The SVG content to cache
   * @returns {Promise<boolean>} - Whether the caching was successful
   */
  static async cacheQR(cacheKey, svgContent) {
    if (!firebaseInitialized) {
      return false;
    }

    try {
      const bucket = admin.storage().bucket();
      const file = bucket.file(this.getCachePath(cacheKey));

      await file.save(svgContent, {
        metadata: {
          contentType: 'image/svg+xml',
          cacheControl: 'public, max-age=31536000', // Cache for 1 year
        },
      });

      // Make the file publicly accessible
      await file.makePublic();

      console.log(`QR code cached successfully: ${cacheKey}`);
      return true;
    } catch (error) {
      console.error('Error caching QR code:', error);
      return false;
    }
  }

  /**
   * Get the public URL for a cached QR code
   * @param {string} cacheKey - The cache key
   * @returns {string} - The public URL
   */
  static getCachedQRUrl(cacheKey) {
    if (!firebaseInitialized) {
      return null;
    }

    const bucket = admin.storage().bucket();
    return `https://storage.googleapis.com/${bucket.name}/${this.getCachePath(cacheKey)}`;
  }
}

// Default users (in production, use proper database with hashed passwords)
const defaultUsers = [
  {
    id: 'admin-1',
    username: 'admin',
    email: '<EMAIL>',
    password: 'admin123', // In production, this should be hashed
    role: 'admin',
    createdAt: Date.now()
  },
  {
    id: 'editor-1',
    username: 'editor',
    email: '<EMAIL>',
    password: 'editor123', // In production, this should be hashed
    role: 'editor',
    createdAt: Date.now()
  },
  {
    id: 'viewer-1',
    username: 'viewer',
    email: '<EMAIL>',
    password: 'viewer123', // In production, this should be hashed
    role: 'viewer',
    createdAt: Date.now()
  }
];

// Firebase authentication middleware with fallback
async function authenticateToken(req, res, next) {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
      return res.status(401).json({ error: 'Access token required' });
    }

    // If Firebase is initialized, try Firebase authentication first
    if (firebaseInitialized) {
      try {
        // Try Firebase ID token verification first
        const decodedToken = await admin.auth().verifyIdToken(token);

        // Get user profile from Firestore
        const userDoc = await admin.firestore().collection('users').doc(decodedToken.uid).get();

        if (!userDoc.exists) {
          return res.status(403).json({ error: 'User profile not found' });
        }

        const userData = userDoc.data();
        req.user = {
          id: decodedToken.uid,
          uid: decodedToken.uid,
          email: decodedToken.email,
          username: userData.username,
          role: userData.role
        };

        return next();
      } catch (firebaseError) {
        console.log('Firebase auth failed, trying fallback:', firebaseError.message);
      }
    }

    // Fallback to old session-based authentication for development
    const session = userSessions.get(token);
    if (!session) {
      return res.status(403).json({ error: 'Invalid or expired token' });
    }

    req.user = session.user;
    next();
  } catch (error) {
    console.error('Authentication error:', error);
    return res.status(403).json({ error: 'Invalid or expired token' });
  }
}

// Permission check middleware
function requirePermission(permission) {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const userPermissions = getRolePermissions(req.user.role);
    if (!userPermissions[permission]) {
      return res.status(403).json({ error: 'Insufficient permissions' });
    }

    next();
  };
}

// Get role permissions
function getRolePermissions(role) {
  switch (role) {
    case 'viewer':
      return {
        canView: true,
        canEdit: false,
        canDelete: false,
        canManageUsers: false,
        canCreateBooks: false,
        canEditBooks: false,
        canDeleteBooks: false,
        canCreateChapters: false,
        canDeleteChapters: false,
      };
    case 'editor':
      return {
        canView: true,
        canEdit: true,
        canDelete: false,
        canManageUsers: false,
        canCreateBooks: false,
        canEditBooks: false,
        canDeleteBooks: false,
        canCreateChapters: false,
        canDeleteChapters: false,
      };
    case 'admin':
      return {
        canView: true,
        canEdit: true,
        canDelete: true,
        canManageUsers: false, // Content admin - no user management
        canCreateBooks: true,
        canEditBooks: true,
        canDeleteBooks: true,
        canCreateChapters: true,
        canDeleteChapters: true,
      };
    case 'super-admin':
      return {
        canView: true,
        canEdit: true,
        canDelete: true,
        canManageUsers: true, // Only super-admin can manage users
        canCreateBooks: true,
        canEditBooks: true,
        canDeleteBooks: true,
        canCreateChapters: true,
        canDeleteChapters: true,
      };
    default:
      return {
        canView: false,
        canEdit: false,
        canDelete: false,
        canManageUsers: false,
        canCreateBooks: false,
        canEditBooks: false,
        canDeleteBooks: false,
        canCreateChapters: false,
        canDeleteChapters: false,
      };
  }
}

function drawRoundedRect(ctx, x, y, width, height, radius) {
  ctx.beginPath();
  ctx.moveTo(x + radius, y);
  ctx.lineTo(x + width - radius, y);
  ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
  ctx.lineTo(x + width, y + height - radius);
  ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
  ctx.lineTo(x + radius, y + height);
  ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
  ctx.lineTo(x, y + radius);
  ctx.quadraticCurveTo(x, y, x + radius, y);
  ctx.closePath();
  ctx.fill();
}

// Helper function to create stylized QR code with rounded dots and corner blocks
async function createSmoothQRCode(data, itemType, color = '#1f2937', backgroundColor = '#ffffff') {
  try {
    console.log(`Generating stylized QR code for ${itemType} with color ${color} and background ${backgroundColor}`);

    // First generate the QR code data matrix
    const qrData = QRCode.create(data, {
      errorCorrectionLevel: 'M'
    });

    const modules = qrData.modules;
    const size = modules.size;
    const canvasSize = 800;
    const margin = 40; // Margin in pixels
    const moduleSize = (canvasSize - 2 * margin) / size;

    // Create high-resolution canvas
    const canvas = createCanvas(canvasSize, canvasSize);
    const ctx = canvas.getContext('2d');

    // Fill background
    ctx.fillStyle = backgroundColor;
    ctx.fillRect(0, 0, canvasSize, canvasSize);

    // Set QR code color
    ctx.fillStyle = color;

    // Helper function to check if a module is dark
    const isDark = (x, y) => {
      if (x < 0 || y < 0 || x >= size || y >= size) return false;
      return modules.get(x, y);
    };

    // Helper function to check if position is a finder pattern (corner squares)
    const isFinderPattern = (x, y) => {
      return (x < 9 && y < 9) || // Top-left
             (x >= size - 8 && y < 9) || // Top-right
             (x < 9 && y >= size - 8); // Bottom-left
    };

    // Draw stylized QR code
    for (let y = 0; y < size; y++) {
      for (let x = 0; x < size; x++) {
        if (isDark(x, y)) {
          const pixelX = margin + x * moduleSize;
          const pixelY = margin + y * moduleSize;

          if (isFinderPattern(x, y)) {
            // Draw rounded rectangles for finder patterns
            drawRoundedRect(ctx, pixelX, pixelY, moduleSize, moduleSize, moduleSize * 0.2);
          } else {
            // Draw rounded dots for data modules
            const radius = moduleSize * 0.4;
            const centerX = pixelX + moduleSize / 2;
            const centerY = pixelY + moduleSize / 2;

            ctx.beginPath();
            ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
            ctx.fill();
          }
        }
      }
    }

    console.log('Converting stylized canvas to PNG buffer...');
    const buffer = canvas.toBuffer('image/png');

    if (!buffer) {
      throw new Error('Failed to generate QR code buffer');
    }

    console.log('Stylized QR code generated successfully, buffer size:', buffer.length);
    return buffer;

  } catch (error) {
    console.error('Error in createSmoothQRCode:', error);
    console.error('Error details:', error.message);
    throw new Error(`QR code generation failed: ${error.message}`);
  }
}

// Helper function to draw rounded rectangles
function hexToRgb(hex) {
  const bigint = parseInt(hex.slice(1), 16);
  const r = (bigint >> 16) & 255;
  const g = (bigint >> 8) & 255;
  const b = bigint & 255;
  return [r, g, b];
}

// Generate SVG-based QR code for caching (without colors)
async function createSVGQRCode(data) {
  try {
    console.log(`Generating SVG QR code for data: ${data.substring(0, 50)}...`);

    // Generate the QR code data matrix
    const qrData = QRCode.create(data, {
      errorCorrectionLevel: 'M'
    });

    const modules = qrData.modules;
    const size = modules.size;
    const viewBoxSize = 800;
    const margin = 40;
    const moduleSize = (viewBoxSize - 2 * margin) / size;

    // Helper function to check if a module is dark
    const isDark = (x, y) => {
      if (x < 0 || y < 0 || x >= size || y >= size) return false;
      return modules.get(x, y);
    };

    // Helper function to check if position is in finder pattern
    const isFinderPattern = (x, y) => {
      return (x < 7 && y < 7) || // Top-left
             (x >= size - 7 && y < 7) || // Top-right
             (x < 7 && y >= size - 7); // Bottom-left
    };

    // Start building SVG
    let svgContent = `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 ${viewBoxSize} ${viewBoxSize}">`;

    // Add background rectangle (will be styled with CSS variables)
    svgContent += `<rect width="${viewBoxSize}" height="${viewBoxSize}" fill="var(--qr-bg-color, #ffffff)"/>`;

    // Add QR code modules
    for (let y = 0; y < size; y++) {
      for (let x = 0; x < size; x++) {
        if (isDark(x, y)) {
          const pixelX = margin + x * moduleSize;
          const pixelY = margin + y * moduleSize;

          if (isFinderPattern(x, y)) {
            // Draw rounded rectangles for finder patterns
            const radius = moduleSize * 0.2;
            svgContent += `<rect x="${pixelX}" y="${pixelY}" width="${moduleSize}" height="${moduleSize}" rx="${radius}" ry="${radius}" fill="var(--qr-fg-color, #1f2937)"/>`;
          } else {
            // Draw rounded dots for data modules
            const radius = moduleSize * 0.4;
            const centerX = pixelX + moduleSize / 2;
            const centerY = pixelY + moduleSize / 2;
            svgContent += `<circle cx="${centerX}" cy="${centerY}" r="${radius}" fill="var(--qr-fg-color, #1f2937)"/>`;
          }
        }
      }
    }

    svgContent += '</svg>';

    console.log('SVG QR code generated successfully');
    return svgContent;

  } catch (error) {
    console.error('Error in createSVGQRCode:', error);
    throw new Error(`SVG QR code generation failed: ${error.message}`);
  }
}

function rgbToHex(r, g, b) {
  return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
}

function adjustColor(hex, percent) {
  const [r, g, b] = hexToRgb(hex);
  const newR = Math.min(255, Math.max(0, r + Math.floor(r * percent / 100)));
  const newG = Math.min(255, Math.max(0, g + Math.floor(g * percent / 100)));
  const newB = Math.min(255, Math.max(0, b + Math.floor(b * percent / 100)));
  return rgbToHex(newR, newG, newB);
}

function generatePalette(primary, secondary, accent) {
  return {
    primaryColor: primary,
    primaryLight: adjustColor(primary, 20),
    primaryDark: adjustColor(primary, -20),
    secondaryColor: secondary,
    secondaryLight: adjustColor(secondary, 20),
    secondaryDark: adjustColor(secondary, -20),
    accentColor: accent,
    accentLight: adjustColor(accent, 20),
    accentDark: adjustColor(accent, -20),
    backgroundColor: '#f7fafc', // Default light background
    textColor: '#1a202c', // Default dark text
    headerColor: '#ffffff', // Default white header text
    linkColor: primary, // Default link color to primary
  };
}



// Authentication endpoints
app.post('/api/auth/login', async (req, res) => {
  try {
    const { username, password } = req.body;

    if (!username || !password) {
      return res.status(400).json({ error: 'Username and password required' });
    }

    // Find user (in production, use proper database lookup with hashed passwords)
    const user = defaultUsers.find(u => u.username === username && u.password === password);

    if (!user) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    // Generate session token
    const token = crypto.randomBytes(32).toString('hex');
    const session = {
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        role: user.role,
        createdAt: user.createdAt,
        lastLogin: Date.now()
      },
      createdAt: Date.now()
    };

    // Store session (expires in 24 hours)
    userSessions.set(token, session);
    setTimeout(() => userSessions.delete(token), 24 * 60 * 60 * 1000);

    res.json({
      user: session.user,
      token: token,
      permissions: getRolePermissions(user.role)
    });

  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/auth/logout', authenticateToken, async (req, res) => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (token) {
      userSessions.delete(token);
    }

    res.json({ message: 'Logged out successfully' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Create a new user (admin only)
app.post('/api/users', authenticateToken, requirePermission('canManageUsers'), async (req, res) => {
  try {
    if (!firebaseInitialized) {
      return res.status(500).json({ error: 'Firebase not initialized' });
    }

    const { email, password, username, role } = req.body;

    if (!email || !password || !username || !role) {
      return res.status(400).json({ error: 'Email, password, username, and role are required' });
    }

    // Validate role
    const validRoles = ['viewer', 'editor', 'admin'];
    if (!validRoles.includes(role)) {
      return res.status(400).json({ error: 'Invalid role' });
    }

    // Create user in Firebase Auth using Admin SDK
    const userRecord = await admin.auth().createUser({
      email: email,
      password: password,
      displayName: username,
      emailVerified: false
    });

    // Create user profile in Firestore
    const userProfile = {
      uid: userRecord.uid,
      email: email,
      username: username,
      role: role,
      createdAt: Date.now()
    };

    await admin.firestore().collection('users').doc(userRecord.uid).set(userProfile);

    // Return user data (without sensitive info)
    const userData = {
      id: userRecord.uid,
      username: username,
      email: email,
      role: role,
      createdAt: Date.now()
    };

    res.status(201).json(userData);
  } catch (error) {
    console.error('Error creating user:', error);
    res.status(500).json({ error: error.message });
  }
});

// Delete a user (admin only)
app.delete('/api/users/:uid', authenticateToken, requirePermission('canManageUsers'), async (req, res) => {
  try {
    if (!firebaseInitialized) {
      return res.status(500).json({ error: 'Firebase not initialized' });
    }

    const { uid } = req.params;

    if (!uid) {
      return res.status(400).json({ error: 'User UID is required' });
    }

    // Prevent users from deleting themselves
    if (uid === req.user.uid) {
      return res.status(400).json({ error: 'Cannot delete your own account' });
    }

    // Delete user from Firebase Auth
    await admin.auth().deleteUser(uid);

    // Delete user profile from Firestore
    await admin.firestore().collection('users').doc(uid).delete();

    res.json({ message: 'User deleted successfully' });
  } catch (error) {
    console.error('Error deleting user:', error);
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/auth/me', authenticateToken, async (req, res) => {
  try {
    res.json({
      user: req.user,
      permissions: getRolePermissions(req.user.role)
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Get user profile (requires authentication)
app.get('/api/user/profile', authenticateToken, async (req, res) => {
  try {
    res.json(req.user);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Get all books (requires authentication)
app.get('/api/books', authenticateToken, requirePermission('canView'), async (req, res) => {
  const books = await readBooks();
  res.json(books);
});

// Add a new book (admin only)
app.post('/api/books', authenticateToken, requirePermission('canCreateBooks'), async (req, res) => {
  try {
    if (!firebaseInitialized) {
      return res.status(500).json({ error: 'Firebase not initialized' });
    }

    const books = await readBooks();
    const newBookId = generateMeaningfulId('book', books);
    const newBook = {
      title: req.body.title,
      description: req.body.description || '',
      createdAt: admin.firestore.FieldValue.serverTimestamp()
    };

    const db = admin.firestore();
    await db.collection('books').doc(newBookId).set(newBook);

    res.json({
      id: newBookId,
      title: newBook.title,
      description: newBook.description,
      chapters: []
    });
  } catch (error) {
    console.error('Error creating book:', error);
    res.status(500).json({ error: error.message });
  }
});

// Add a chapter to a book (admin only)
app.post('/api/books/:bookId/chapters', authenticateToken, requirePermission('canCreateChapters'), async (req, res) => {
  try {
    if (!firebaseInitialized) {
      return res.status(500).json({ error: 'Firebase not initialized' });
    }

    const db = admin.firestore();
    const bookRef = db.collection('books').doc(req.params.bookId);
    const bookDoc = await bookRef.get();

    if (!bookDoc.exists) {
      return res.status(404).json({ error: 'Book not found' });
    }

    const books = await readBooks();
    const newChapterId = generateMeaningfulId('chapter', books, req.params.bookId);
    const newChapter = {
      title: req.body.title,
      createdAt: admin.firestore.FieldValue.serverTimestamp()
    };

    await bookRef.collection('chapters').doc(newChapterId).set(newChapter);

    res.json({
      id: newChapterId,
      title: newChapter.title,
      items: []
    });
  } catch (error) {
    console.error('Error creating chapter:', error);
    res.status(500).json({ error: error.message });
  }
});

// Add an item to a chapter (editor or admin)
app.post('/api/books/:bookId/chapters/:chapterId/items', 
  authenticateToken, 
  requirePermission('canEdit'), 
  upload.single('image'), // Use multer to handle image upload
  async (req, res) => {
  try {
    if (!firebaseInitialized) {
      return res.status(500).json({ error: 'Firebase not initialized' });
    }

    const db = admin.firestore();
    const bucket = admin.storage().bucket();
    const { bookId, chapterId } = req.params;
    const itemData = req.body.item ? JSON.parse(req.body.item) : req.body;

    const chapterRef = db.collection('books').doc(bookId).collection('chapters').doc(chapterId);
    const chapterDoc = await chapterRef.get();

    if (!chapterDoc.exists) {
      return res.status(404).json({ error: 'Chapter not found' });
    }

    const books = await readBooks();
    const newItemId = generateMeaningfulId('item', books, bookId, chapterId);
    
    let imageUrl = '';
    let imagePath = '';
    let imageFileName = '';
    let imageSize = 0;
    let imageMimeType = '';
    let itemUrl = itemData.url || '';

    // Handle image upload or URL
    if (req.file) {
      // Process uploaded file
      const file = req.file;
      const fileName = `${newItemId}_${Date.now()}.${file.originalname.split('.').pop()}`;
      imagePath = `images/${bookId}/${chapterId}/${fileName}`;

      const fileRef = bucket.file(imagePath);
      await fileRef.save(file.buffer, {
        metadata: { contentType: file.mimetype },
      });
      await fileRef.makePublic(); // Ensure it's publicly accessible
      
      imageUrl = `https://storage.googleapis.com/${bucket.name}/${imagePath}`;
      imageFileName = file.originalname;
      imageSize = file.size;
      imageMimeType = file.mimetype;
      itemUrl = ''; // Clear itemUrl if a file is uploaded
    } else if (itemData.type === 'image' && itemData.url) {
      // Use provided URL for image item
      imageUrl = itemData.url;
      imagePath = ''; // Clear imagePath if using URL
      imageFileName = '';
      imageSize = 0;
      imageMimeType = '';
      itemUrl = itemData.url; // Ensure itemUrl is set
    }

    const newItem = {
      ...itemData,
      id: newItemId,
      imageUrl,
      imagePath,
      imageFileName,
      imageSize,
      imageMimeType,
      url: itemUrl, // Ensure the item's URL is saved
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
    };

    await chapterRef.collection('items').doc(newItemId).set(newItem);

    res.json({
      id: newItemId,
      ...newItem,
    });
    console.log('Server response for new item creation:', { id: newItemId, ...newItem });
  } catch (error) {
    console.error('Error creating item:', error);
    res.status(500).json({ error: error.message });
  }
});

// Update a book (admin only)
app.put('/api/books/:bookId', authenticateToken, requirePermission('canEditBooks'), async (req, res) => {
  try {
    if (!firebaseInitialized) {
      return res.status(500).json({ error: 'Firebase not initialized' });
    }

    const db = admin.firestore();
    const bookRef = db.collection('books').doc(req.params.bookId);
    const bookDoc = await bookRef.get();

    if (!bookDoc.exists) {
      return res.status(404).json({ error: 'Book not found' });
    }

    const { title, description, theme } = req.body;
    const themeToStore = {};
    if (theme) {
      if (theme.primaryColor) themeToStore.primaryColor = theme.primaryColor;
      if (theme.secondaryColor) themeToStore.secondaryColor = theme.secondaryColor;
      if (theme.accentColor) themeToStore.accentColor = theme.accentColor;
    }

    const updatedData = {
      title,
      description,
      theme: themeToStore,
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    };

    await bookRef.update(updatedData);

    res.json({
      id: req.params.bookId,
      ...updatedData,
    });
  } catch (error) {
    console.error('Error updating book:', error);
    res.status(500).json({ error: error.message });
  }
});

// Delete a book
app.delete('/api/books/:bookId', async (req, res) => {
  try {
    if (!firebaseInitialized) {
      return res.status(500).json({ error: 'Firebase not initialized' });
    }

    const db = admin.firestore();
    const bookRef = db.collection('books').doc(req.params.bookId);
    const bookDoc = await bookRef.get();

    if (!bookDoc.exists) {
      return res.status(404).json({ error: 'Book not found' });
    }

    // Delete all chapters and their items
    const chaptersSnapshot = await bookRef.collection('chapters').get();
    const batch = db.batch();

    for (const chapterDoc of chaptersSnapshot.docs) {
      // Delete all items in this chapter
      const itemsSnapshot = await chapterDoc.ref.collection('items').get();
      itemsSnapshot.docs.forEach(itemDoc => {
        batch.delete(itemDoc.ref);
      });
      // Delete the chapter
      batch.delete(chapterDoc.ref);
    }

    // Delete the book
    batch.delete(bookRef);

    await batch.commit();
    res.json({ message: 'Book deleted successfully' });
  } catch (error) {
    console.error('Error deleting book:', error);
    res.status(500).json({ error: error.message });
  }
});

// Delete a chapter
app.delete('/api/books/:bookId/chapters/:chapterId', async (req, res) => {
  try {
    if (!firebaseInitialized) {
      return res.status(500).json({ error: 'Firebase not initialized' });
    }

    const db = admin.firestore();
    const chapterRef = db.collection('books').doc(req.params.bookId).collection('chapters').doc(req.params.chapterId);
    const chapterDoc = await chapterRef.get();

    if (!chapterDoc.exists) {
      return res.status(404).json({ error: 'Chapter not found' });
    }

    // Delete all items in this chapter
    const itemsSnapshot = await chapterRef.collection('items').get();
    const batch = db.batch();

    itemsSnapshot.docs.forEach(itemDoc => {
      batch.delete(itemDoc.ref);
    });

    // Delete the chapter
    batch.delete(chapterRef);

    await batch.commit();
    res.json({ message: 'Chapter deleted successfully' });
  } catch (error) {
    console.error('Error deleting chapter:', error);
    res.status(500).json({ error: error.message });
  }
});

// Update an item in a chapter (editor or admin)
app.put('/api/books/:bookId/chapters/:chapterId/items/:itemId', 
  authenticateToken, 
  requirePermission('canEdit'), 
  upload.single('image'), // Use multer to handle image upload
  async (req, res) => {
  try {
    if (!firebaseInitialized) {
      return res.status(500).json({ error: 'Firebase not initialized' });
    }

    const db = admin.firestore();
    const bucket = admin.storage().bucket();
    const { bookId, chapterId, itemId } = req.params;
    const itemRef = db.collection('books').doc(bookId).collection('chapters').doc(chapterId).collection('items').doc(itemId);
    const itemDoc = await itemRef.get();

    if (!itemDoc.exists) {
      return res.status(404).json({ error: 'Item not found' });
    }

    const currentItemData = itemDoc.data();
    const incomingItemData = req.body.item ? JSON.parse(req.body.item) : req.body;

    const updatedData = { ...currentItemData, ...incomingItemData };

    // Handle image upload
    if (req.file) {
      if (currentItemData.imagePath) {
        try {
          await bucket.file(currentItemData.imagePath).delete();
          console.log('Old image successfully deleted from storage.');
        } catch (deleteError) {
          console.error('Error deleting old image from storage:', deleteError);
        }
      }

      const file = req.file;
      const fileName = `${itemId}_${Date.now()}.${file.originalname.split('.').pop()}`;
      const imagePath = `images/${bookId}/${chapterId}/${fileName}`;
      const fileRef = bucket.file(imagePath);
      await fileRef.save(file.buffer, {
        metadata: { contentType: file.mimetype },
      });
      await fileRef.makePublic();

      updatedData.imageUrl = `https://storage.googleapis.com/${bucket.name}/${imagePath}`;
      updatedData.imagePath = imagePath;
      updatedData.imageFileName = file.originalname;
      updatedData.imageSize = file.size;
      updatedData.imageMimeType = file.mimetype;
      updatedData.url = ''; // Clear itemUrl if a file is uploaded
    } else if (updatedData.type === 'image') {
      if (updatedData.url && updatedData.url !== currentItemData.url) {
        if (currentItemData.imagePath) {
          try {
            await bucket.file(currentItemData.imagePath).delete();
            console.log('Old image successfully deleted from storage.');
          } catch (deleteError) {
            console.error('Error deleting old image from storage:', deleteError);
          }
        }
        updatedData.imagePath = '';
        updatedData.imageFileName = '';
        updatedData.imageSize = 0;
        updatedData.imageMimeType = '';
        updatedData.imageUrl = updatedData.url;
      } else if (!updatedData.imageUrl && currentItemData.imagePath) {
        try {
          await bucket.file(currentItemData.imagePath).delete();
          console.log('Old image successfully deleted from storage.');
        } catch (deleteError) {
          console.error('Error deleting old image from storage:', deleteError);
        }
        updatedData.imageUrl = '';
        updatedData.imagePath = '';
        updatedData.imageFileName = '';
        updatedData.imageSize = 0;
        updatedData.imageMimeType = '';
        updatedData.url = '';
      }
    }

    updatedData.updatedAt = admin.firestore.FieldValue.serverTimestamp();

    await itemRef.update(updatedData);

    res.json({
      id: itemId,
      ...updatedData,
    });
    console.log('Server response for item update:', { id: itemId, ...updatedData });
  } catch (error) {
    console.error('Error updating item:', error);
    res.status(500).json({ error: error.message });
  }
});

// Delete an item (admin only)
app.delete('/api/books/:bookId/chapters/:chapterId/items/:itemId', authenticateToken, requirePermission('canDelete'), async (req, res) => {
  try {
    if (!firebaseInitialized) {
      return res.status(500).json({ error: 'Firebase not initialized' });
    }

    const db = admin.firestore();
    const itemRef = db.collection('books').doc(req.params.bookId).collection('chapters').doc(req.params.chapterId).collection('items').doc(req.params.itemId);
    const itemDoc = await itemRef.get();

    if (!itemDoc.exists) {
      return res.status(404).json({ error: 'Item not found' });
    }

    const itemData = itemDoc.data();

    // Delete associated image from Firebase Storage if it exists
    if (itemData.imagePath) {
      try {
        const bucket = admin.storage().bucket();
        await bucket.file(itemData.imagePath).delete();
        console.log('Associated image deleted:', itemData.imagePath);
      } catch (imageError) {
        console.error('Error deleting associated image:', imageError);
        // Continue with item deletion even if image deletion fails
      }
    }

    await itemRef.delete();
    res.json({ message: 'Item deleted successfully' });
  } catch (error) {
    console.error('Error deleting item:', error);
    res.status(500).json({ error: error.message });
  }
});

// Upload image for an item
app.post('/api/books/:bookId/chapters/:chapterId/items/:itemId/upload-image',
  authenticateToken,
  requirePermission('canEdit'),
  upload.single('image'),
  async (req, res) => {
    try {
      if (!firebaseInitialized) {
        return res.status(500).json({ error: 'Firebase not initialized' });
      }

      if (!req.file) {
        return res.status(400).json({ error: 'No image file provided' });
      }

      const { bookId, chapterId, itemId } = req.params;
      const file = req.file;

      // Validate file type
      if (!file.mimetype.startsWith('image/')) {
        return res.status(400).json({ error: 'Only image files are allowed' });
      }

      const db = admin.firestore();
      const bucket = admin.storage().bucket();

      // Check if item exists
      const itemRef = db.collection('books').doc(bookId).collection('chapters').doc(chapterId).collection('items').doc(itemId);
      const itemDoc = await itemRef.get();

      if (!itemDoc.exists) {
        return res.status(404).json({ error: 'Item not found' });
      }

      const itemData = itemDoc.data();

      // Delete old image if it exists
      if (itemData.imagePath) {
        try {
          await bucket.file(itemData.imagePath).delete();
          console.log('Old image deleted:', itemData.imagePath);
        } catch (deleteError) {
          console.error('Error deleting old image:', deleteError);
          // Continue with upload even if old image deletion fails
        }
      }

      // Generate unique filename
      const timestamp = Date.now();
      const extension = file.originalname.split('.').pop() || 'jpg';
      const fileName = `${itemId}_${timestamp}.${extension}`;
      const imagePath = `images/${bookId}/${chapterId}/${fileName}`;

      // Upload to Firebase Storage
      const fileRef = bucket.file(imagePath);
      const stream = fileRef.createWriteStream({
        metadata: {
          contentType: file.mimetype,
        },
      });

      await new Promise((resolve, reject) => {
        stream.on('error', reject);
        stream.on('finish', resolve);
        stream.end(file.buffer);
      });

      // Get download URL
      await fileRef.makePublic();
      const imageUrl = `https://storage.googleapis.com/${bucket.name}/${imagePath}`;

      // Update item with image information
      await itemRef.update({
        imageUrl,
        imagePath,
        imageFileName: file.originalname,
        imageSize: file.size,
        imageMimeType: file.mimetype,
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      });

      res.json({
        message: 'Image uploaded successfully',
        imageUrl,
        imagePath,
        fileName: file.originalname,
        size: file.size,
        mimeType: file.mimetype
      });

    } catch (error) {
      console.error('Error uploading image:', error);
      res.status(500).json({ error: error.message });
    }
  }
);

// Get image for an item
app.get('/api/images/:bookId/:chapterId/:fileName', async (req, res) => {
  try {
    if (!firebaseInitialized) {
      return res.status(500).json({ error: 'Firebase not initialized' });
    }

    const { bookId, chapterId, fileName } = req.params;
    const imagePath = `images/${bookId}/${chapterId}/${fileName}`;

    const bucket = admin.storage().bucket();
    const file = bucket.file(imagePath);

    // Check if file exists
    const [exists] = await file.exists();
    if (!exists) {
      return res.status(404).json({ error: 'Image not found' });
    }

    // Get file metadata
    const [metadata] = await file.getMetadata();

    // Set appropriate headers
    res.set({
      'Content-Type': metadata.contentType || 'image/jpeg',
      'Cache-Control': 'public, max-age=31536000', // Cache for 1 year
    });

    // Stream the file
    file.createReadStream().pipe(res);

  } catch (error) {
    console.error('Error serving image:', error);
    res.status(500).json({ error: error.message });
  }
});

// Generate QR code for an item (DEPRECATED - use /api/qr-cached instead)
app.get('/api/qr/:bookId/:chapterId/:itemId', async (req, res) => {
  try {
    const { bookId, chapterId, itemId } = req.params;
    const books = await readBooks();

    const book = books.find(b => b.id === bookId);
    if (!book) {
      return res.status(404).json({ error: 'Book not found' });
    }

    const chapter = book.chapters.find(c => c.id === chapterId);
    if (!chapter) {
      return res.status(404).json({ error: 'Chapter not found' });
    }

    const item = chapter.items.find(i => i.id === itemId);
    if (!item) {
      return res.status(404).json({ error: 'Item not found' });
    }

    // Create permanent URL using item IDs (no expiring tokens)
    // Use the request host for the display URL to ensure it works in all environments
    const baseUrl = process.env.NODE_ENV === 'production'
      ? `${req.protocol}://${req.get('host')}`
      : `${req.protocol}://${getNetworkIP()}:${PORT}`;
    const displayUrl = `${baseUrl}/item/${bookId}/${chapterId}/${itemId}`;

    const itemData = {
      bookId,
      chapterId,
      itemId,
      bookTitle: book.title,
      chapterTitle: chapter.title,
      itemTitle: item.title,
      itemType: item.type,
      item: item,
      createdAt: Date.now()
    };

    // Generate smooth QR code with default gray color and white background
    const qrBuffer = await createSmoothQRCode(displayUrl, item.type, '#1f2937', '#ffffff');
    const qrCodeDataURL = `data:image/png;base64,${qrBuffer.toString('base64')}`;

    res.json({
      qrCode: qrCodeDataURL,
      data: itemData,
      item: item,
      displayUrl: displayUrl
    });

  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Generate stylized QR code with custom colors (DEPRECATED - use /api/qr-cached instead)
app.get('/api/qr-styled/:bookId/:chapterId/:itemId', async (req, res) => {
  try {
    const { bookId, chapterId, itemId } = req.params;
    const color = req.query.color || '#1f2937';
    const backgroundColor = req.query.backgroundColor || '#ffffff';
    const books = await readBooks();

    const book = books.find(b => b.id === bookId);
    if (!book) {
      return res.status(404).json({ error: 'Book not found' });
    }

    const chapter = book.chapters.find(c => c.id === chapterId);
    if (!chapter) {
      return res.status(404).json({ error: 'Chapter not found' });
    }

    const item = chapter.items.find(i => i.id === itemId);
    if (!item) {
      return res.status(404).json({ error: 'Item not found' });
    }







    // Create permanent URL using item IDs (no expiring tokens)
    // Use the request host for the display URL to ensure it works in all environments
    const baseUrl = process.env.NODE_ENV === 'production'
      ? `${req.protocol}://${req.get('host')}`
      : `${req.protocol}://${getNetworkIP()}:${PORT}`;
    const displayUrl = `${baseUrl}/item/${bookId}/${chapterId}/${itemId}`;

    const itemData = {
      bookId,
      chapterId,
      itemId,
      bookTitle: book.title,
      chapterTitle: chapter.title,
      itemTitle: item.title,
      itemType: item.type,
      item: item,
      createdAt: Date.now()
    };

    // Generate smooth QR code with selected colors
    const qrBuffer = await createSmoothQRCode(displayUrl, item.type, color, backgroundColor);
    const qrCodeDataURL = `data:image/png;base64,${qrBuffer.toString('base64')}`;

    res.json({
      qrCode: qrCodeDataURL,
      data: itemData,
      item: item,
      styled: true,
      color: color,
      backgroundColor: backgroundColor,
      displayUrl: displayUrl
    });

  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// New cached QR code endpoint with real-time color customization
app.get('/api/qr-cached/:bookId/:chapterId/:itemId', async (req, res) => {
  try {
    const { bookId, chapterId, itemId } = req.params;
    const color = req.query.color || '#1f2937';
    const backgroundColor = req.query.backgroundColor || '#ffffff';
    const books = await readBooks();

    const book = books.find(b => b.id === bookId);
    if (!book) {
      return res.status(404).json({ error: 'Book not found' });
    }

    const chapter = book.chapters.find(c => c.id === chapterId);
    if (!chapter) {
      return res.status(404).json({ error: 'Chapter not found' });
    }

    const item = chapter.items.find(i => i.id === itemId);
    if (!item) {
      return res.status(404).json({ error: 'Item not found' });
    }

    // Create the display URL for the QR code
    const baseUrl = process.env.NODE_ENV === 'production'
      ? 'https://textbook-platform.netlify.app'
      : `http://${getNetworkIP()}:5173`;

    const displayUrl = `${baseUrl}/item/${bookId}/${chapterId}/${itemId}`;

    // Generate cache key based on the display URL
    const cacheKey = QRCodeCache.generateCacheKey(displayUrl);

    // Check if QR code is cached
    let svgContent = await QRCodeCache.getCachedQR(cacheKey);

    if (!svgContent) {
      // Generate and cache the SVG QR code
      console.log('QR code not cached, generating new SVG...');
      svgContent = await createSVGQRCode(displayUrl);
      await QRCodeCache.cacheQR(cacheKey, svgContent);
    } else {
      console.log('Using cached QR code');
    }

    // Apply colors to the SVG using CSS variables
    const styledSvg = svgContent.replace(
      '<svg xmlns="http://www.w3.org/2000/svg"',
      `<svg xmlns="http://www.w3.org/2000/svg" style="--qr-fg-color: ${color}; --qr-bg-color: ${backgroundColor};"`
    );

    // Set appropriate headers for SVG
    res.set({
      'Content-Type': 'image/svg+xml',
      'Cache-Control': 'public, max-age=3600', // Cache for 1 hour
    });

    res.send(styledSvg);

  } catch (error) {
    console.error('Error serving cached QR code:', error);
    res.status(500).json({ error: error.message });
  }
});

// Download QR code endpoint (forces download with proper headers)
app.get('/api/qr-download/:bookId/:chapterId/:itemId', async (req, res) => {
  try {
    const { bookId, chapterId, itemId } = req.params;
    const color = req.query.color || '#1f2937';
    const backgroundColor = req.query.backgroundColor || '#ffffff';
    const books = await readBooks();

    const book = books.find(b => b.id === bookId);
    if (!book) {
      return res.status(404).json({ error: 'Book not found' });
    }

    const chapter = book.chapters.find(c => c.id === chapterId);
    if (!chapter) {
      return res.status(404).json({ error: 'Chapter not found' });
    }

    const item = chapter.items.find(i => i.id === itemId);
    if (!item) {
      return res.status(404).json({ error: 'Item not found' });
    }

    // Create the display URL for the QR code
    const baseUrl = process.env.NODE_ENV === 'production'
      ? 'https://textbook-platform.netlify.app'
      : `http://${getNetworkIP()}:5173`;

    const displayUrl = `${baseUrl}/item/${bookId}/${chapterId}/${itemId}`;

    // Generate cache key based on the display URL
    const cacheKey = QRCodeCache.generateCacheKey(displayUrl);

    // Check if QR code is cached
    let svgContent = await QRCodeCache.getCachedQR(cacheKey);

    if (!svgContent) {
      // Generate and cache the SVG QR code
      console.log('QR code not cached, generating new SVG for download...');
      svgContent = await createSVGQRCode(displayUrl);
      await QRCodeCache.cacheQR(cacheKey, svgContent);
    } else {
      console.log('Using cached QR code for download');
    }

    // Apply colors to the SVG using CSS variables
    const styledSvg = svgContent.replace(
      '<svg xmlns="http://www.w3.org/2000/svg"',
      `<svg xmlns="http://www.w3.org/2000/svg" style="--qr-fg-color: ${color}; --qr-bg-color: ${backgroundColor};"`
    );

    // Create a safe filename
    const safeTitle = item.title.replace(/[^a-z0-9]/gi, '_').toLowerCase();
    const filename = `qr-code-${safeTitle}.svg`;

    // Set headers to force download
    res.set({
      'Content-Type': 'image/svg+xml',
      'Content-Disposition': `attachment; filename="${filename}"`,
      'Cache-Control': 'no-cache',
    });

    res.send(styledSvg);

  } catch (error) {
    console.error('Error downloading QR code:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get QR code info endpoint (returns metadata without generating the QR code)
app.get('/api/qr-info/:bookId/:chapterId/:itemId', async (req, res) => {
  try {
    const { bookId, chapterId, itemId } = req.params;
    const books = await readBooks();

    const book = books.find(b => b.id === bookId);
    if (!book) {
      return res.status(404).json({ error: 'Book not found' });
    }

    const chapter = book.chapters.find(c => c.id === chapterId);
    if (!chapter) {
      return res.status(404).json({ error: 'Chapter not found' });
    }

    const item = chapter.items.find(i => i.id === itemId);
    if (!item) {
      return res.status(404).json({ error: 'Item not found' });
    }

    // Create the display URL for the QR code
    const baseUrl = process.env.NODE_ENV === 'production'
      ? 'https://textbook-platform.netlify.app'
      : `http://${getNetworkIP()}:5173`;

    const displayUrl = `${baseUrl}/item/${bookId}/${chapterId}/${itemId}`;

    // Generate cache key
    const cacheKey = QRCodeCache.generateCacheKey(displayUrl);

    // Check if cached
    const isCached = await QRCodeCache.isCached(cacheKey);

    // Return QR code info and URL template
    res.json({
      data: {
        bookId,
        chapterId,
        itemId,
        bookTitle: book.title,
        chapterTitle: chapter.title,
        itemTitle: item.title,
        itemType: item.type,
        url: displayUrl
      },
      item: item,
      displayUrl: displayUrl,
      cached: isCached,
      qrUrl: `/api/qr-cached/${bookId}/${chapterId}/${itemId}`,
      cacheKey: cacheKey
    });

  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Permanent display page for QR code access using item IDs
app.get('/item/:bookId/:chapterId/:itemId', async (req, res) => {
  try {
    const { bookId, chapterId, itemId } = req.params;
    const books = await readBooks();

    const book = books.find(b => b.id === bookId);
    if (!book) {
      return res.status(404).send(`
        <!DOCTYPE html>
        <html lang="en">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Item Not Found</title>
          <style>
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                   background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                   margin: 0; padding: 20px; min-height: 100vh; display: flex; align-items: center; justify-content: center; }
            .container { background: white; padding: 40px; border-radius: 20px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); text-align: center; max-width: 400px; }
            h1 { color: #e53e3e; margin-bottom: 20px; }
            p { color: #666; line-height: 1.6; }
          </style>
        </head>
        <body>
          <div class="container">
            <h1>📚 Book Not Found</h1>
            <p>The requested book could not be found.</p>
          </div>
        </body>
        </html>
      `);
    }

    const chapter = book.chapters.find(c => c.id === chapterId);
    if (!chapter) {
      return res.status(404).send(`
        <!DOCTYPE html>
        <html lang="en">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Chapter Not Found</title>
          <style>
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                   background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                   margin: 0; padding: 20px; min-height: 100vh; display: flex; align-items: center; justify-content: center; }
            .container { background: white; padding: 40px; border-radius: 20px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); text-align: center; max-width: 400px; }
            h1 { color: #e53e3e; margin-bottom: 20px; }
            p { color: #666; line-height: 1.6; }
          </style>
        </head>
        <body>
          <div class="container">
            <h1>📖 Chapter Not Found</h1>
            <p>The requested chapter could not be found.</p>
          </div>
        </body>
        </html>
      `);
    }

    const item = chapter.items.find(i => i.id === itemId);
    if (!item) {
      return res.status(404).send(`
        <!DOCTYPE html>
        <html lang="en">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Item Not Found</title>
          <style>
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                   background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                   margin: 0; padding: 20px; min-height: 100vh; display: flex; align-items: center; justify-content: center; }
            .container { background: white; padding: 40px; border-radius: 20px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); text-align: center; max-width: 400px; }
            h1 { color: #e53e3e; margin-bottom: 20px; }
            p { color: #666; line-height: 1.6; }
          </style>
        </head>
        <body>
          <div class="container">
            <h1>📄 Item Not Found</h1>
            <p>The requested item could not be found.</p>
          </div>
        </body>
        </html>
      `);
    }

    // Handle different item types
    if (item.type === 'link' && item.url) {
      // For links, redirect directly
      return res.redirect(item.url);
    }

    // For other types, show beautiful display page
    const displayPage = generateDisplayPage(item, book.title, chapter.title, book.theme);
    res.send(displayPage);

  } catch (error) {
    res.status(500).send('Internal Server Error');
  }
});

// Migration endpoint - migrate data from books.json to Firestore (super-admin only)
app.post('/api/migrate-to-firestore', authenticateToken, async (req, res) => {
  try {
    // Check if user is super-admin
    if (req.user.role !== 'super-admin') {
      return res.status(403).json({ error: 'Only super-admin can perform migration' });
    }

    if (!firebaseInitialized) {
      return res.status(500).json({ error: 'Firebase not initialized' });
    }

    console.log('🔄 Starting migration from books.json to Firestore...');

    // Read the existing books.json file
    const booksData = await fs.readFile(BOOKS_FILE, 'utf8');
    const books = JSON.parse(booksData);

    console.log(`📚 Found ${books.length} books to migrate`);

    const db = admin.firestore();
    let migratedBooks = 0;
    let migratedChapters = 0;
    let migratedItems = 0;

    // Migrate each book to Firestore
    for (const book of books) {
      console.log(`📖 Migrating book: ${book.title}`);

      // Create book document
      const bookRef = db.collection('books').doc(book.id);
      await bookRef.set({
        id: book.id,
        title: book.title,
        description: book.description,
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      });
      migratedBooks++;

      // Migrate chapters
      if (book.chapters && book.chapters.length > 0) {
        console.log(`  📑 Migrating ${book.chapters.length} chapters`);

        for (const chapter of book.chapters) {
          const chapterRef = bookRef.collection('chapters').doc(chapter.id);
          await chapterRef.set({
            id: chapter.id,
            title: chapter.title,
            createdAt: admin.firestore.FieldValue.serverTimestamp(),
            updatedAt: admin.firestore.FieldValue.serverTimestamp()
          });
          migratedChapters++;

          // Migrate items
          if (chapter.items && chapter.items.length > 0) {
            console.log(`    📄 Migrating ${chapter.items.length} items`);

            for (const item of chapter.items) {
              const itemRef = chapterRef.collection('items').doc(item.id);
              await itemRef.set({
                ...item,
                createdAt: admin.firestore.FieldValue.serverTimestamp(),
                updatedAt: admin.firestore.FieldValue.serverTimestamp()
              });
              migratedItems++;
            }
          }
        }
      }
    }

    console.log('✅ Migration completed successfully!');

    res.json({
      success: true,
      message: 'Migration completed successfully',
      stats: {
        books: migratedBooks,
        chapters: migratedChapters,
        items: migratedItems
      }
    });

  } catch (error) {
    console.error('❌ Migration failed:', error);
    res.status(500).json({ error: `Migration failed: ${error.message}` });
  }
});

// Generate PDF test results
app.post('/api/generate-test-pdf', async (req, res) => {
  try {
    const testData = JSON.parse(req.body.testData);

    // Create PDF document
    const doc = new PDFDocument({
      size: 'A4',
      margin: 50,
      info: {
        Title: `Test Results - ${testData.testTitle}`,
        Author: testData.userName,
        Subject: `${testData.bookTitle} - ${testData.chapterTitle}`,
        Creator: 'Textbook Platform'
      }
    });

    // Set response headers for PDF download
    const safeTestName = testData.testTitle.replace(/[^a-z0-9]/gi, '_').toLowerCase();
    const safeUserName = testData.userName.replace(/[^a-z0-9]/gi, '_').toLowerCase();
    const filename = `${safeUserName}_${safeTestName}_results.pdf`;

    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);

    // Pipe PDF to response
    doc.pipe(res);

    // Helper function for Arabic text support
    function processText(text) {
      try {
        return bidi(text, { dir: 'auto' });
      } catch (error) {
        return text; // Fallback to original text if bidi processing fails
      }
    }

    // Calculate score and percentage
    const percentage = Math.round((testData.score / testData.total) * 100);
    const date = new Date(testData.date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    // Header section with gradient-like effect using rectangles
    doc.rect(0, 0, doc.page.width, 120)
       .fillAndStroke('#4f46e5', '#4f46e5');

    // Title
    doc.fillColor('#ffffff')
       .fontSize(28)
       .font('Helvetica-Bold')
       .text('🏆 Test Results', 50, 30, { align: 'center' });

    // Test details
    doc.fontSize(16)
       .text(processText(`${testData.bookTitle} → ${testData.chapterTitle}`), 50, 65, { align: 'center' });

    doc.fontSize(18)
       .font('Helvetica-Bold')
       .text(processText(testData.testTitle), 50, 85, { align: 'center' });

    // Student info section
    doc.fillColor('#1e293b')
       .fontSize(20)
       .font('Helvetica-Bold')
       .text(`Student: ${processText(testData.userName)}`, 50, 150);

    doc.fontSize(14)
       .fillColor('#64748b')
       .font('Helvetica')
       .text(`Date: ${date}`, 50, 175);

    // Score section with circular progress simulation
    const centerX = doc.page.width / 2;
    const scoreY = 220;

    // Score background circle
    doc.circle(centerX, scoreY, 60)
       .fillAndStroke('#f1f5f9', '#e2e8f0');

    // Score text
    doc.fillColor('#1a202c')
       .fontSize(24)
       .font('Helvetica-Bold')
       .text(`${testData.score}/${testData.total}`, centerX - 30, scoreY - 15, { width: 60, align: 'center' });

    doc.fontSize(16)
       .fillColor('#64748b')
       .text(`${percentage}%`, centerX - 20, scoreY + 5, { width: 40, align: 'center' });

    // Performance badge
    let performanceText = '';
    let badgeColor = '';
    if (percentage >= 90) {
      performanceText = 'Excellent!';
      badgeColor = '#10b981';
    } else if (percentage >= 80) {
      performanceText = 'Great Job!';
      badgeColor = '#10b981';
    } else if (percentage >= 70) {
      performanceText = 'Good Work!';
      badgeColor = '#f59e0b';
    } else if (percentage >= 60) {
      performanceText = 'Keep Trying!';
      badgeColor = '#f59e0b';
    } else {
      performanceText = 'Study More!';
      badgeColor = '#ef4444';
    }

    doc.rect(centerX - 50, scoreY + 40, 100, 25)
       .fillAndStroke(badgeColor, badgeColor);

    doc.fillColor('#ffffff')
       .fontSize(12)
       .font('Helvetica-Bold')
       .text(performanceText, centerX - 45, scoreY + 48, { width: 90, align: 'center' });

    // Questions review section
    let currentY = 350;
    doc.fillColor('#1e293b')
       .fontSize(20)
       .font('Helvetica-Bold')
       .text('Question Review', 50, currentY);

    currentY += 40;

    // Process each question
    testData.questions.forEach((question, index) => {
      if (currentY > doc.page.height - 150) {
        doc.addPage();
        currentY = 50;
      }

      const isCorrect = testData.userAnswers[index] === question.correctAnswer;
      const userAnswer = testData.userAnswers[index] !== undefined ?
        question.options[testData.userAnswers[index]]?.text || 'Not answered' : 'Not answered';
      const correctAnswer = question.options[question.correctAnswer]?.text || 'No answer';

      // Question card background
      const cardColor = isCorrect ? '#ecfdf5' : '#fef2f2';
      const borderColor = isCorrect ? '#10b981' : '#ef4444';

      doc.rect(50, currentY - 5, doc.page.width - 100, 80)
         .fillAndStroke(cardColor, borderColor);

      // Status icon
      doc.fillColor(borderColor)
         .fontSize(16)
         .font('Helvetica-Bold')
         .text(isCorrect ? '✓' : '✗', 65, currentY + 10);

      // Question text
      doc.fillColor('#1e293b')
         .fontSize(14)
         .font('Helvetica-Bold')
         .text(`Q${index + 1}: ${processText(question.question)}`, 90, currentY + 5, {
           width: doc.page.width - 150,
           height: 20,
           ellipsis: true
         });

      // User answer
      doc.fillColor('#64748b')
         .fontSize(12)
         .font('Helvetica')
         .text(`Your answer: ${processText(userAnswer)}`, 90, currentY + 25, {
           width: doc.page.width - 150,
           height: 15,
           ellipsis: true
         });

      // Correct answer (if wrong)
      if (!isCorrect) {
        doc.fillColor('#059669')
           .fontSize(12)
           .font('Helvetica-Bold')
           .text(`Correct answer: ${processText(correctAnswer)}`, 90, currentY + 45, {
             width: doc.page.width - 150,
             height: 15,
             ellipsis: true
           });
      }

      currentY += 100;
    });

    // Footer
    doc.fillColor('#64748b')
       .fontSize(12)
       .font('Helvetica')
       .text(`📚 Generated for ${processText(testData.userName)} • ${date}`, 50, doc.page.height - 50, {
         align: 'center',
         width: doc.page.width - 100
       });

    // Finalize PDF
    doc.end();

  } catch (error) {
    console.error('PDF generation error:', error);
    res.status(500).json({ error: 'Failed to generate PDF' });
  }
});

// Legacy display page for backward compatibility (will show deprecation notice)
app.get('/display/:token', async (req, res) => {
  try {
    const { token } = req.params;
    const tokenData = qrTokens.get(token);

    if (!tokenData) {
      return res.status(404).send(`
        <!DOCTYPE html>
        <html lang="en">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Invalid or Expired Link</title>
          <style>
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                   background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                   margin: 0; padding: 20px; min-height: 100vh; display: flex; align-items: center; justify-content: center; }
            .container { background: white; padding: 40px; border-radius: 20px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); text-align: center; max-width: 400px; }
            h1 { color: #e53e3e; margin-bottom: 20px; }
            p { color: #666; line-height: 1.6; }
          </style>
        </head>
        <body>
          <div class="container">
            <h1>🔒 Access Denied</h1>
            <p>This link is invalid or has expired. Please generate a new QR code.</p>
            <p><small>Note: QR codes now use permanent links that never expire.</small></p>
          </div>
        </body>
        </html>
      `);
    }

    const { item, bookTitle, chapterTitle } = tokenData;

    // Handle different item types
    if (item.type === 'link' && item.url) {
      // For links, redirect directly
      return res.redirect(item.url);
    }

    // For other types, show beautiful display page
    const displayPage = generateDisplayPage(item, bookTitle, chapterTitle);
    res.send(displayPage);

  } catch (error) {
    res.status(500).send('Internal Server Error');
  }
});

// Generate beautiful display page
function generateDisplayPage(item, bookTitle, chapterTitle, baseTheme = {}) {
  const theme = generatePalette(
    baseTheme.primaryColor || '#4f46e5',
    baseTheme.secondaryColor || '#7c3aed',
    baseTheme.accentColor || '#F59E0B'
  );
  const getItemIcon = (type) => {
    const icons = {
      question: '❓',
      test: '📝',
      text: '📄',
      image: '🖼️',
      link: '🔗',
      map: '🗺️',
      diagram: '📊',
      'timed-question': '⏰'
    };
    return icons[type] || '📋';
  };

  // Prepare shuffled data before rendering
  const shuffledQuestion = item.type === 'question' && item.options ? shuffleQuestionOptions(item) : item;
  const shuffledTestQuestions = item.type === 'test' && item.testQuestions ?
    shuffleArray(item.testQuestions).map(question => shuffleQuestionOptions(question)) : [];

  const renderItemContent = (item) => {
    switch (item.type) {
      case 'question':
        return `
          <div class="question-container">
            <h2 class="question-title">${shuffledQuestion.question || shuffledQuestion.title}</h2>
            ${shuffledQuestion.options ? `
              <div class="options-container" id="optionsContainer">
                ${shuffledQuestion.options.map((option, index) => `
                  <div class="option" data-index="${index}" onclick="selectOption(${index}, ${shuffledQuestion.correctAnswer})">
                    <span class="option-letter">${String.fromCharCode(65 + index)}</span>
                    <span class="option-text">${option.text}</span>
                  </div>
                `).join('')}
              </div>
              <div id="result" class="result-container" style="display: none;">
                <div id="resultMessage" class="result-message"></div>
                <button onclick="resetQuestion()" class="reset-button">Try Again</button>
              </div>
            ` : ''}
          </div>
        `;
      case 'test':
        return `
          <div class="test-container">
            <div class="test-header">
              <h2 class="test-title">📝 ${item.title}</h2>
              <div class="test-progress">
                <span id="currentQuestion">1</span> of <span id="totalQuestions">${shuffledTestQuestions.length || 0}</span>
              </div>
            </div>

            <div class="test-content">
              ${shuffledTestQuestions.length > 0 ? shuffledTestQuestions.map((question, qIndex) => `
                <div class="test-question ${qIndex === 0 ? 'active' : 'hidden'}" data-question="${qIndex}">
                  <h3 class="question-text">${question.question}</h3>
                  <div class="test-options">
                    ${question.options.map((option, oIndex) => `
                      <div class="test-option" data-question="${qIndex}" data-option="${oIndex}" onclick="selectTestOption(${qIndex}, ${oIndex}, ${question.correctAnswer})">
                        <span class="option-letter">${String.fromCharCode(65 + oIndex)}</span>
                        <span class="option-text">${option.text}</span>
                      </div>
                    `).join('')}
                  </div>
                </div>
              `).join('') : '<p>No questions available</p>'}
            </div>

            <div class="test-navigation">
              <button id="prevBtn" onclick="previousQuestion()" disabled>Previous</button>
              <button id="nextBtn" onclick="nextQuestion()">Next</button>
              <button id="finishBtn" onclick="finishTest()" style="display: none;">Finish Test</button>
            </div>

            <div id="testResults" class="test-results" style="display: none;">
              <div class="results-header">
                <h3>Test Complete!</h3>
                <div class="score-circle">
                  <div class="score-text">
                    <span id="scoreNumber">0</span>/<span id="totalScore">${item.testQuestions?.length || 0}</span>
                  </div>
                </div>
              </div>

              <div class="results-list">
                <h4>Question Review:</h4>
                <div id="questionReview"></div>
              </div>

              <div class="results-actions">
                <button onclick="retakeTest()" class="retry-button">Retake Test</button>
                <button onclick="promptForNameAndDownload()" class="download-button">Download Results</button>
              </div>
            </div>
          </div>
        `;
      case 'text':
        return `
          <div class="text-container">
            <div class="text-content">${item.content || 'No content available'}</div>
          </div>
        `;
      case 'image':
        return `
          <div class="image-container">
            <img src="${item.imageUrl}" alt="${item.title}" class="item-image" onerror="this.onerror=null;this.src='/icons/image.svg';">
          </div>
        `;
      case 'map':
        return `
          <div class="image-container">
            <div class="image-placeholder">
              <span class="image-icon">🗺️</span>
              <p>Map: ${item.title}</p>
              <small>Map image would be displayed here</small>
            </div>
          </div>
        `;
      case 'diagram':
        return `
          <div class="image-container">
            <div class="image-placeholder">
              <span class="image-icon">📊</span>
              <p>Diagram: ${item.title}</p>
              <small>Diagram image would be displayed here</small>
            </div>
          </div>
        `;
      case 'timed-question':
        return `
          <div class="timed-question-container">
            <h2 class="question-title">${item.question || item.title}</h2>
            <div class="timer-container">
              <div class="timer-display" id="timerDisplay">
                <span class="timer-icon">⏰</span>
                <span class="timer-text" id="timerText">${item.revealTimeSeconds || 30}</span>
                <span class="timer-label">seconds</span>
              </div>
            </div>
            <div class="answer-container" id="answerContainer" style="display: none;">
              <h3 class="answer-title">Answer:</h3>
              <div class="answer-content">${item.timedAnswer || 'No answer provided'}</div>
            </div>
            <button id="startTimerBtn" class="start-timer-button" onclick="startTimer()">Start Timer</button>
            <button id="resetTimerBtn" class="reset-timer-button" onclick="resetTimer()" style="display: none;">Reset</button>
          </div>
        `;
      default:
        return `<div class="default-content">Content not available</div>`;
    }
  };

  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${item.title} - ${bookTitle}</title>
      <style>
        :root {
          --primary-color: ${theme.primaryColor};
          --primary-light: ${theme.primaryLight};
          --primary-dark: ${theme.primaryDark};
          --secondary-color: ${theme.secondaryColor};
          --secondary-light: ${theme.secondaryLight};
          --secondary-dark: ${theme.secondaryDark};
          --accent-color: ${theme.accentColor};
          --accent-light: ${theme.accentLight};
          --accent-dark: ${theme.accentDark};
          --background-color: ${theme.backgroundColor};
          --text-color: ${theme.textColor};
          --header-color: ${theme.headerColor};
          --link-color: ${theme.linkColor};
        }

        * { margin: 0; padding: 0; box-sizing: border-box; }

        body {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
          background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
          min-height: 100vh;
          padding: 20px;
          line-height: 1.6;
        }

        .container {
          max-width: 800px;
          margin: 0 auto;
          background: white;
          border-radius: 20px;
          box-shadow: 0 20px 40px rgba(0,0,0,0.1);
          overflow: hidden;
          animation: slideUp 0.6s ease-out;
        }

        @keyframes slideUp {
          from { opacity: 0; transform: translateY(30px); }
          to { opacity: 1; transform: translateY(0); }
        }

        .header {
          background: white;
          color: #333333;
          padding: 30px;
          text-align: center;
        }

        .item-icon {
          font-size: 3rem;
          margin-bottom: 15px;
          display: block;
        }

        .item-title {
          font-size: 2rem;
          font-weight: 700;
          margin-bottom: 10px;
        }

        .breadcrumb {
          opacity: 0.9;
          font-size: 0.9rem;
        }

        .content {
          padding: 40px;
          color: var(--text-color);
        }

        .question-container {
          text-align: center;
        }

        .question-title {
          font-size: 1.5rem;
          color: var(--text-color);
          margin-bottom: 30px;
          font-weight: 600;
        }

        .options-container {
          display: grid;
          gap: 15px;
          max-width: 600px;
          margin: 0 auto;
        }

        .option {
          display: flex;
          align-items: center;
          padding: 20px;
          background: #f7fafc;
          border-radius: 12px;
          border: 2px solid #e2e8f0;
          transition: all 0.3s ease;
          position: relative;
          cursor: pointer;
        }

        .option:hover {
          background: #edf2f7;
          border-color: #cbd5e0;
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .option.selected {
          background: #ebf8ff;
          border-color: var(--primary-color);
        }

        .option.correct {
          background: #f0fff4;
          border-color: #68d391;
          box-shadow: 0 4px 12px rgba(104, 211, 145, 0.2);
        }

        .option.incorrect {
          background: #fed7d7;
          border-color: #fc8181;
          box-shadow: 0 4px 12px rgba(252, 129, 129, 0.2);
        }

        .option.disabled {
          cursor: not-allowed;
          opacity: 0.7;
        }

        .option-letter {
          background: var(--primary-color);
          color: white;
          width: 40px;
          height: 40px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: bold;
          margin-right: 15px;
          flex-shrink: 0;
        }

        .option.correct .option-letter {
          background: #38a169;
        }

        .option.incorrect .option-letter {
          background: #e53e3e;
        }

        .option-text {
          flex: 1;
          font-size: 1.1rem;
        }

        .correct-indicator {
          color: #38a169;
          font-size: 1.5rem;
          font-weight: bold;
        }

        .incorrect-indicator {
          color: #e53e3e;
          font-size: 1.5rem;
          font-weight: bold;
        }

        .result-container {
          margin-top: 30px;
          text-align: center;
          padding: 20px;
          border-radius: 12px;
          animation: fadeIn 0.5s ease-out;
        }

        .result-message {
          font-size: 1.2rem;
          font-weight: 600;
          margin-bottom: 20px;
        }

        .result-message.correct {
          color: #38a169;
        }

        .result-message.incorrect {
          color: #e53e3e;
        }

        .reset-button {
          background: var(--primary-color);
          color: white;
          border: none;
          padding: 12px 24px;
          border-radius: 8px;
          font-size: 1rem;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.3s ease;
        }

        .reset-button:hover {
          background: #4338ca;
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
        }

        @keyframes fadeIn {
          from { opacity: 0; transform: translateY(20px); }
          to { opacity: 1; transform: translateY(0); }
        }

        .text-container {
          max-width: 700px;
          margin: 0 auto;
        }

        .text-content {
          font-size: 1.2rem;
          line-height: 1.8;
          color: #2d3748;
          background: #f7fafc;
          padding: 30px;
          border-radius: 12px;
          border-left: 4px solid #4f46e5;
        }

        .item-image {
          max-width: 100%;
          height: auto;
          border-radius: 12px;
          box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        .image-container {
          text-align: center;
        }

        .image-placeholder {
          background: #f7fafc;
          border: 2px dashed #cbd5e0;
          border-radius: 12px;
          padding: 60px 30px;
          color: #718096;
        }

        .image-icon {
          font-size: 4rem;
          display: block;
          margin-bottom: 20px;
        }

        .timed-question-container {
          text-align: center;
          max-width: 600px;
          margin: 0 auto;
        }

        .timer-container {
          margin: 30px 0;
        }

        .timer-display {
          background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
          color: white;
          padding: 30px;
          border-radius: 20px;
          display: inline-flex;
          align-items: center;
          gap: 15px;
          font-size: 2rem;
          font-weight: bold;
          box-shadow: 0 10px 25px rgba(79, 70, 229, 0.3);
          animation: pulse 2s infinite;
        }

        @keyframes pulse {
          0%, 100% { transform: scale(1); }
          50% { transform: scale(1.05); }
        }

        .timer-icon {
          font-size: 2.5rem;
        }

        .timer-text {
          font-size: 3rem;
          min-width: 80px;
        }

        .timer-label {
          font-size: 1.2rem;
          opacity: 0.9;
        }

        .answer-container {
          background: #f0fff4;
          border: 2px solid #68d391;
          border-radius: 12px;
          padding: 30px;
          margin: 30px 0;
          animation: slideDown 0.5s ease-out;
        }

        .answer-title {
          color: #38a169;
          font-size: 1.5rem;
          margin-bottom: 15px;
          font-weight: 600;
        }

        .answer-content {
          font-size: 1.2rem;
          line-height: 1.6;
          color: #2d3748;
          background: white;
          padding: 20px;
          border-radius: 8px;
          border-left: 4px solid #38a169;
        }

        @keyframes slideDown {
          from { opacity: 0; transform: translateY(-20px); }
          to { opacity: 1; transform: translateY(0); }
        }

        .start-timer-button, .reset-timer-button {
          background: #4f46e5;
          color: white;
          border: none;
          padding: 15px 30px;
          border-radius: 10px;
          font-size: 1.1rem;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.3s ease;
          margin: 10px;
        }

        .start-timer-button:hover, .reset-timer-button:hover {
          background: #4338ca;
          transform: translateY(-2px);
          box-shadow: 0 6px 20px rgba(79, 70, 229, 0.4);
        }

        .reset-timer-button {
          background: #6b7280;
        }

        .reset-timer-button:hover {
          background: #4b5563;
        }

        .footer {
          background: #f7fafc;
          padding: 20px 40px;
          text-align: center;
          color: #718096;
          font-size: 0.9rem;
          border-top: 1px solid #e2e8f0;
        }

        /* Test Styles */
        .test-container {
          max-width: 700px;
          margin: 0 auto;
        }

        .test-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 30px;
          padding-bottom: 20px;
          border-bottom: 2px solid #e2e8f0;
        }

        .test-title {
          font-size: 1.8rem;
          color: #1a202c;
          font-weight: 600;
        }

        .test-progress {
          background: #4f46e5;
          color: white;
          padding: 8px 16px;
          border-radius: 20px;
          font-weight: 600;
        }

        .test-content {
          margin-bottom: 30px;
        }

        .test-question {
          animation: fadeIn 0.3s ease-out;
        }

        .test-question.hidden {
          display: none;
        }

        .question-text {
          font-size: 1.4rem;
          color: #2d3748;
          margin-bottom: 25px;
          font-weight: 600;
          line-height: 1.5;
        }

        .test-options {
          display: grid;
          gap: 12px;
        }

        .test-option {
          display: flex;
          align-items: center;
          padding: 16px;
          background: #f7fafc;
          border-radius: 10px;
          border: 2px solid #e2e8f0;
          transition: all 0.3s ease;
          cursor: pointer;
        }

        .test-option:hover {
          background: #edf2f7;
          border-color: #cbd5e0;
          transform: translateY(-1px);
        }

        .test-option.selected {
          background: #ebf8ff;
          border-color: #4299e1;
        }

        .test-option.correct {
          background: #f0fff4;
          border-color: #68d391;
        }

        .test-option.incorrect {
          background: #fed7d7;
          border-color: #fc8181;
        }

        .test-navigation {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-top: 30px;
        }

        .test-navigation button {
          background: #4f46e5;
          color: white;
          border: none;
          padding: 12px 24px;
          border-radius: 8px;
          font-size: 1rem;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.3s ease;
        }

        .test-navigation button:hover:not(:disabled) {
          background: #4338ca;
          transform: translateY(-2px);
        }

        .test-navigation button:disabled {
          background: #9ca3af;
          cursor: not-allowed;
          transform: none;
        }

        .test-results {
          text-align: center;
          animation: fadeIn 0.5s ease-out;
        }

        .results-header {
          margin-bottom: 30px;
        }

        .results-header h3 {
          font-size: 2rem;
          color: #1a202c;
          margin-bottom: 20px;
        }

        .score-circle {
          width: 120px;
          height: 120px;
          border-radius: 50%;
          background: conic-gradient(#4f46e5 0deg, #4f46e5 var(--score-angle, 0deg), #e2e8f0 var(--score-angle, 0deg));
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 auto 20px;
          position: relative;
        }

        .score-circle::before {
          content: '';
          width: 80px;
          height: 80px;
          background: white;
          border-radius: 50%;
          position: absolute;
        }

        .score-text {
          position: relative;
          z-index: 1;
          font-size: 1.5rem;
          font-weight: bold;
          color: #1a202c;
        }

        .results-list {
          text-align: left;
          margin-bottom: 30px;
        }

        .results-list h4 {
          font-size: 1.3rem;
          margin-bottom: 15px;
          color: #2d3748;
        }

        .question-result {
          display: flex;
          align-items: center;
          padding: 12px;
          margin-bottom: 8px;
          border-radius: 8px;
          background: #f7fafc;
        }

        .question-result.correct {
          background: #f0fff4;
          color: #38a169;
        }

        .question-result.incorrect {
          background: #fed7d7;
          color: #e53e3e;
        }

        .results-actions {
          display: flex;
          gap: 15px;
          justify-content: center;
        }

        .retry-button, .download-button {
          background: #4f46e5;
          color: white;
          border: none;
          padding: 12px 24px;
          border-radius: 8px;
          font-size: 1rem;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.3s ease;
        }

        .download-button {
          background: #059669;
        }

        .retry-button:hover {
          background: #4338ca;
        }

        .download-button:hover {
          background: #047857;
        }

        @media (max-width: 768px) {
          body { padding: 10px; }
          .container { border-radius: 15px; }
          .header { padding: 20px; }
          .item-title { font-size: 1.5rem; }
          .content { padding: 20px; }
          .question-title { font-size: 1.3rem; }
          .option { padding: 15px; }
          .option-text { font-size: 1rem; }
          .text-content { font-size: 1.1rem; padding: 20px; }
          .test-header { flex-direction: column; gap: 15px; text-align: center; }
          .test-title { font-size: 1.5rem; }
          .question-text { font-size: 1.2rem; }
          .results-actions { flex-direction: column; }
        }
      </style>
      <script>
        function selectOption(selectedIndex, correctAnswer) {
          const options = document.querySelectorAll('.option');
          const resultContainer = document.getElementById('result');
          const resultMessage = document.getElementById('resultMessage');

          // Disable all options
          options.forEach(option => {
            option.classList.add('disabled');
            option.onclick = null;
          });

          // Mark selected option
          options[selectedIndex].classList.add('selected');

          // Show correct/incorrect styling
          if (selectedIndex === correctAnswer) {
            options[selectedIndex].classList.add('correct');
            options[selectedIndex].innerHTML += '<span class="correct-indicator">✓</span>';
            resultMessage.textContent = '🎉 Correct! Well done!';
            resultMessage.className = 'result-message correct';
          } else {
            options[selectedIndex].classList.add('incorrect');
            options[selectedIndex].innerHTML += '<span class="incorrect-indicator">✗</span>';
            options[correctAnswer].classList.add('correct');
            options[correctAnswer].innerHTML += '<span class="correct-indicator">✓</span>';
            resultMessage.textContent = '❌ Incorrect. The correct answer is highlighted.';
            resultMessage.className = 'result-message incorrect';
          }

          // Show result
          resultContainer.style.display = 'block';
        }

        function resetQuestion() {
          const options = document.querySelectorAll('.option');
          const resultContainer = document.getElementById('result');

          // Reset all options
          options.forEach((option, index) => {
            option.className = 'option';
            option.onclick = () => selectOption(index, ${shuffledQuestion.correctAnswer || 0});
            // Remove indicators
            const indicators = option.querySelectorAll('.correct-indicator, .incorrect-indicator');
            indicators.forEach(indicator => indicator.remove());
          });

          // Hide result
          resultContainer.style.display = 'none';
        }

        // Timed question functionality
        let timerInterval;
        let timeRemaining = ${item.revealTimeSeconds || 30};

        function startTimer() {
          const startBtn = document.getElementById('startTimerBtn');
          const resetBtn = document.getElementById('resetTimerBtn');
          const timerText = document.getElementById('timerText');
          const answerContainer = document.getElementById('answerContainer');

          startBtn.style.display = 'none';
          resetBtn.style.display = 'inline-block';

          timerInterval = setInterval(() => {
            timeRemaining--;
            timerText.textContent = timeRemaining;

            if (timeRemaining <= 0) {
              clearInterval(timerInterval);
              answerContainer.style.display = 'block';
              timerText.textContent = '0';
              resetBtn.textContent = 'Try Again';
            }
          }, 1000);
        }

        function resetTimer() {
          clearInterval(timerInterval);
          timeRemaining = ${item.revealTimeSeconds || 30};

          const startBtn = document.getElementById('startTimerBtn');
          const resetBtn = document.getElementById('resetTimerBtn');
          const timerText = document.getElementById('timerText');
          const answerContainer = document.getElementById('answerContainer');

          timerText.textContent = timeRemaining;
          answerContainer.style.display = 'none';
          startBtn.style.display = 'inline-block';
          resetBtn.style.display = 'none';
          resetBtn.textContent = 'Reset';
        }

        // Test functionality
        let currentQuestionIndex = 0;
        let testAnswers = [];
        let testQuestions = ${JSON.stringify(shuffledTestQuestions)};

        function selectTestOption(questionIndex, optionIndex, correctAnswer) {
          const questionDiv = document.querySelector(\`[data-question="\${questionIndex}"]\`);
          const options = questionDiv.querySelectorAll('.test-option');

          // Clear previous selections for this question
          options.forEach(opt => {
            opt.classList.remove('selected');
          });

          // Mark selected option
          options[optionIndex].classList.add('selected');

          // Store answer
          testAnswers[questionIndex] = optionIndex;

          // Update navigation buttons
          updateNavigationButtons();
        }

        function nextQuestion() {
          if (currentQuestionIndex < testQuestions.length - 1) {
            // Hide current question
            document.querySelector(\`[data-question="\${currentQuestionIndex}"]\`).classList.add('hidden');

            // Show next question
            currentQuestionIndex++;
            document.querySelector(\`[data-question="\${currentQuestionIndex}"]\`).classList.remove('hidden');

            // Update progress
            document.getElementById('currentQuestion').textContent = currentQuestionIndex + 1;

            updateNavigationButtons();
          }
        }

        function previousQuestion() {
          if (currentQuestionIndex > 0) {
            // Hide current question
            document.querySelector(\`[data-question="\${currentQuestionIndex}"]\`).classList.add('hidden');

            // Show previous question
            currentQuestionIndex--;
            document.querySelector(\`[data-question="\${currentQuestionIndex}"]\`).classList.remove('hidden');

            // Update progress
            document.getElementById('currentQuestion').textContent = currentQuestionIndex + 1;

            updateNavigationButtons();
          }
        }

        function updateNavigationButtons() {
          const prevBtn = document.getElementById('prevBtn');
          const nextBtn = document.getElementById('nextBtn');
          const finishBtn = document.getElementById('finishBtn');

          // Update previous button
          prevBtn.disabled = currentQuestionIndex === 0;

          // Update next/finish buttons
          if (currentQuestionIndex === testQuestions.length - 1) {
            nextBtn.style.display = 'none';
            finishBtn.style.display = 'inline-block';
          } else {
            nextBtn.style.display = 'inline-block';
            finishBtn.style.display = 'none';
          }
        }

        function finishTest() {
          // Calculate score
          let correctCount = 0;
          testQuestions.forEach((question, index) => {
            if (testAnswers[index] === question.correctAnswer) {
              correctCount++;
            }
          });

          // Hide test content
          document.querySelector('.test-content').style.display = 'none';
          document.querySelector('.test-navigation').style.display = 'none';

          // Show results
          const resultsDiv = document.getElementById('testResults');
          resultsDiv.style.display = 'block';

          // Update score display
          document.getElementById('scoreNumber').textContent = correctCount;
          document.getElementById('totalScore').textContent = testQuestions.length;

          // Update score circle
          const scorePercentage = (correctCount / testQuestions.length) * 100;
          const scoreAngle = (scorePercentage / 100) * 360;
          document.querySelector('.score-circle').style.setProperty('--score-angle', \`\${scoreAngle}deg\`);

          // Generate question review
          const reviewDiv = document.getElementById('questionReview');
          reviewDiv.innerHTML = testQuestions.map((question, index) => {
            const isCorrect = testAnswers[index] === question.correctAnswer;
            const userAnswer = testAnswers[index] !== undefined ? question.options[testAnswers[index]]?.text : 'Not answered';
            const correctAnswer = question.options[question.correctAnswer]?.text;

            return \`
              <div class="question-result \${isCorrect ? 'correct' : 'incorrect'}">
                <span style="margin-right: 10px;">\${isCorrect ? '✓' : '✗'}</span>
                <div>
                  <strong>Q\${index + 1}:</strong> \${question.question}<br>
                  <small>Your answer: \${userAnswer}</small>
                  \${!isCorrect ? \`<br><small>Correct answer: \${correctAnswer}</small>\` : ''}
                </div>
              </div>
            \`;
          }).join('');
        }

        function retakeTest() {
          // Reset test state
          currentQuestionIndex = 0;
          testAnswers = [];

          // Hide results
          document.getElementById('testResults').style.display = 'none';

          // Show test content
          document.querySelector('.test-content').style.display = 'block';
          document.querySelector('.test-navigation').style.display = 'flex';

          // Reset all questions
          document.querySelectorAll('.test-question').forEach((q, index) => {
            q.classList.toggle('hidden', index !== 0);
            q.querySelectorAll('.test-option').forEach(opt => {
              opt.classList.remove('selected');
            });
          });

          // Reset progress
          document.getElementById('currentQuestion').textContent = '1';

          // Reset navigation
          updateNavigationButtons();
        }

        function promptForNameAndDownload() {
          const userName = prompt('Please enter your name for the test results:');
          if (userName && userName.trim() !== '') {
            downloadResults(userName.trim());
          }
        }

        function downloadResults(userName = 'Student') {
          // Get score values from DOM elements
          const scoreElement = document.getElementById('scoreNumber');
          const totalElement = document.getElementById('totalScore');
          const currentScore = scoreElement ? parseInt(scoreElement.textContent) : 0;
          const currentTotal = totalElement ? parseInt(totalElement.textContent) : 0;

          // Generate PDF on server and download
          const testData = {
            userName: userName,
            testTitle: '${item.title}',
            bookTitle: '${bookTitle}',
            chapterTitle: '${chapterTitle}',
            score: currentScore,
            total: currentTotal,
            questions: testQuestions,
            userAnswers: testAnswers,
            date: new Date().toISOString()
          };

          // Create form and submit to server for PDF generation
          const form = document.createElement('form');
          form.method = 'POST';
          form.action = '/api/generate-test-pdf';
          form.style.display = 'none';

          const input = document.createElement('input');
          input.type = 'hidden';
          input.name = 'testData';
          input.value = JSON.stringify(testData);

          form.appendChild(input);
          document.body.appendChild(form);
          form.submit();
          document.body.removeChild(form);
        }

      </script>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <span class="item-icon">${getItemIcon(item.type)}</span>
          <h1 class="item-title">${item.title}</h1>
          <div class="breadcrumb">${bookTitle} → ${chapterTitle}</div>
        </div>

        <div class="content">
          ${renderItemContent(item)}
        </div>

        <div class="footer">
          <p>📚 Accessed via QR Code • ${new Date().toLocaleDateString()}</p>
        </div>
      </div>
    </body>
    </html>
  `;
}

console.log('Starting server...');

const server = app.listen(PORT, '0.0.0.0', () => {
  const networkIP = getNetworkIP();
  console.log(`✅ Server running on http://localhost:${PORT}`);
  console.log(`🌐 Network access: http://${networkIP}:${PORT}`);
  console.log(`📚 API available at: http://${networkIP}:${PORT}/api/books`);
  console.log(`📱 QR codes will use: http://${networkIP}:${PORT}`);
});

// Keep the server alive with a simple interval
setInterval(() => {
  // This keeps the event loop active
}, 30000);

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  server.close(() => {
    console.log('Process terminated');
  });
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully');
  server.close(() => {
    console.log('Process terminated');
    process.exit(0);
  });
});
