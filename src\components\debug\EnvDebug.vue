<template>
  <div class="fixed top-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded z-50">
    <h3 class="font-bold">Environment Debug</h3>
    <p><strong>API Base URL:</strong> {{ apiBaseUrl }}</p>
    <p><strong>Mode:</strong> {{ mode }}</p>
    <p><strong>Dev:</strong> {{ isDev }}</p>
    <button @click="hide = true" class="absolute top-1 right-1 text-red-500 hover:text-red-700">×</button>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

const hide = ref(false);
const apiBaseUrl = import.meta.env.VITE_API_BASE_URL;
const mode = import.meta.env.MODE;
const isDev = import.meta.env.DEV;
</script>

<style scoped>
.fixed {
  display: v-bind(hide ? 'none' : 'block');
}
</style>
